# ACME Relay Configuration File
# This file contains all configuration parameters for the ACME relay service

# Server Configuration
server:
  # Address to listen on (default: 127.0.0.1:6060)
  listen_addr: "127.0.0.1:6060"
  
  # HTTP server timeouts
  read_header_timeout: "10s"
  
  # API endpoint path (default: /getcert)
  endpoint_path: "/getcert"

# ACME Configuration
acme:
  # Required: Account email for ACME registration
  account_email: "<EMAIL>"
  
  # Data directory for storing certificates and account information
  data_dir: "./data"
  
  # ACME directory URL (empty for Let's Encrypt production)
  # For staging: https://acme-staging-v02.api.letsencrypt.org/directory
  directory_url: ""
  
  # Certificate renewal settings
  renew_before: "720h" # 30 days (30 * 24h)
  
  # Certificate key type (EC256, EC384, RSA2048, RSA4096, RSA8192)
  key_type: "EC256"

# DNS Provider Configuration (Tencent Cloud)
dns:
  provider: "tencentcloud"
  
  # Tencent Cloud credentials (can also be set via environment variables)
  # TENCENTCLOUD_SECRET_ID and TENCENTCLOUD_SECRET_KEY
  tencentcloud:
    secret_id: ""
    secret_key: ""
  
  # DNS propagation settings
  recursive_nameservers:
    - "**************"
    - "*********"
    - "************"

# Security Configuration
security:
  # Enable domain whitelist (empty list means all domains allowed)
  domain_whitelist: []
  
  # Enable source IP validation (empty list means all IPs allowed)
  allowed_ips: []
  
  # Enable request validation
  validate_requests: false

# Logging Configuration
logging:
  # Log level (debug, info, warn, error)
  level: "info"
  
  # Log format (text, json)
  format: "text"
  
  # Enable request logging
  log_requests: true

# Storage Configuration
storage:
  # Certificate storage directory structure
  certs_dir: "certs"
  
  # Account file names
  account_file: "account.json"
  account_key_file: "account.key"
  
  # File permissions (octal)
  cert_file_mode: 0600
  key_file_mode: 0600
  dir_mode: 0700
