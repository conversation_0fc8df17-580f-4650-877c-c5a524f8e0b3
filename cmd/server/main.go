package main

import (
	"flag"
	"log"
	"net/http"
	"os"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
	"code.ixdev.cn/cnix/cbdv/acme-relay/config"
)

func main() {
	// Parse command line flags
	configFile := flag.String("config", "conf.yaml", "Path to configuration file")
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create ACME manager configuration
	acmeCfg := acmex.DefaultConfig(cfg.ACME.AccountEmail, cfg.ACME.DataDir)
	acmeCfg.CADirURL = cfg.ACME.DirectoryURL
	acmeCfg.RenewBefore = cfg.ACME.RenewBefore

	// Convert key type string to certcrypto.KeyType
	if keyType, err := parseKeyType(cfg.ACME.KeyType); err != nil {
		log.Fatalf("Invalid key type: %v", err)
	} else {
		acmeCfg.KeyType = keyType
	}

	mgr, err := acmex.NewManager(acmeCfg)
	if err != nil {
		log.Fatalf("init manager: %v", err)
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", func(w http.ResponseWriter, r *http.Request) {
		serverName := r.URL.Query().Get("server_name")
		if serverName == "" {
			http.Error(w, "missing server_name", http.StatusBadRequest)
			return
		}

		// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
		ctx := r.Context()
		fullchain, key, err := mgr.GetCertificate(ctx, serverName)
		if err != nil {
			log.Printf("[ERR] issue %s: %v", serverName, err)
			http.Error(w, "issue/renew failed", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/x-pem-file")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(fullchain)
		_, _ = w.Write([]byte("\n"))
		_, _ = w.Write(key)
	})

	addr := getEnv("LISTEN_ADDR", "127.0.0.1:6060")
	srv := &http.Server{
		Addr:              addr,
		Handler:           mux,
		ReadHeaderTimeout: 10 * time.Second,
	}
	log.Printf("certbox listening on http://%s/getcert", addr)
	log.Fatal(srv.ListenAndServe())
}

func getEnv(k, def string) string {
	if v := os.Getenv(k); v != "" {
		return v
	}
	return def
}
