package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config represents the complete configuration structure
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	ACME     ACMEConfig     `yaml:"acme"`
	DNS      DNSConfig      `yaml:"dns"`
	Security SecurityConfig `yaml:"security"`
	Logging  LoggingConfig  `yaml:"logging"`
	Storage  StorageConfig  `yaml:"storage"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	ListenAddr        string        `yaml:"listen_addr"`
	ReadHeaderTimeout time.Duration `yaml:"read_header_timeout"`
	EndpointPath      string        `yaml:"endpoint_path"`
}

// ACMEConfig contains ACME client configuration
type ACMEConfig struct {
	AccountEmail string        `yaml:"account_email"`
	DataDir      string        `yaml:"data_dir"`
	DirectoryURL string        `yaml:"directory_url"`
	RenewBefore  time.Duration `yaml:"renew_before"`
	KeyType      string        `yaml:"key_type"`
}

// DNSConfig contains DNS provider configuration
type DNSConfig struct {
	Provider              string              `yaml:"provider"`
	TencentCloud          TencentCloudConfig  `yaml:"tencentcloud"`
	RecursiveNameservers  []string            `yaml:"recursive_nameservers"`
}

// TencentCloudConfig contains Tencent Cloud DNS provider settings
type TencentCloudConfig struct {
	SecretID  string `yaml:"secret_id"`
	SecretKey string `yaml:"secret_key"`
}

// SecurityConfig contains security-related settings
type SecurityConfig struct {
	DomainWhitelist   []string `yaml:"domain_whitelist"`
	AllowedIPs        []string `yaml:"allowed_ips"`
	ValidateRequests  bool     `yaml:"validate_requests"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level       string `yaml:"level"`
	Format      string `yaml:"format"`
	LogRequests bool   `yaml:"log_requests"`
}

// StorageConfig contains storage-related settings
type StorageConfig struct {
	CertsDir       string      `yaml:"certs_dir"`
	AccountFile    string      `yaml:"account_file"`
	AccountKeyFile string      `yaml:"account_key_file"`
	CertFileMode   os.FileMode `yaml:"cert_file_mode"`
	KeyFileMode    os.FileMode `yaml:"key_file_mode"`
	DirMode        os.FileMode `yaml:"dir_mode"`
}

// LoadConfig loads configuration from a YAML file
func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", filename, err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %w", filename, err)
	}

	// Apply environment variable overrides
	cfg.applyEnvOverrides()

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &cfg, nil
}

// applyEnvOverrides applies environment variable overrides to configuration
func (c *Config) applyEnvOverrides() {
	// Server overrides
	if addr := os.Getenv("LISTEN_ADDR"); addr != "" {
		c.Server.ListenAddr = addr
	}

	// ACME overrides
	if email := os.Getenv("ACME_ACCOUNT_EMAIL"); email != "" {
		c.ACME.AccountEmail = email
	}
	if dataDir := os.Getenv("ACME_DATA_DIR"); dataDir != "" {
		c.ACME.DataDir = dataDir
	}
	if dirURL := os.Getenv("ACME_DIRECTORY_URL"); dirURL != "" {
		c.ACME.DirectoryURL = dirURL
	}

	// DNS provider overrides
	if secretID := os.Getenv("TENCENTCLOUD_SECRET_ID"); secretID != "" {
		c.DNS.TencentCloud.SecretID = secretID
	}
	if secretKey := os.Getenv("TENCENTCLOUD_SECRET_KEY"); secretKey != "" {
		c.DNS.TencentCloud.SecretKey = secretKey
	}
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate required fields
	if c.ACME.AccountEmail == "" {
		return fmt.Errorf("acme.account_email is required")
	}
	if c.ACME.DataDir == "" {
		return fmt.Errorf("acme.data_dir is required")
	}
	if c.Server.ListenAddr == "" {
		return fmt.Errorf("server.listen_addr is required")
	}

	// Validate key type
	validKeyTypes := map[string]bool{
		"EC256":   true,
		"EC384":   true,
		"RSA2048": true,
		"RSA4096": true,
		"RSA8192": true,
	}
	if !validKeyTypes[c.ACME.KeyType] {
		return fmt.Errorf("invalid acme.key_type: %s", c.ACME.KeyType)
	}

	// Validate DNS provider
	if c.DNS.Provider != "tencentcloud" {
		return fmt.Errorf("unsupported dns.provider: %s", c.DNS.Provider)
	}

	// Set defaults for optional fields
	if c.Server.EndpointPath == "" {
		c.Server.EndpointPath = "/getcert"
	}
	if c.Server.ReadHeaderTimeout == 0 {
		c.Server.ReadHeaderTimeout = 10 * time.Second
	}
	if c.ACME.RenewBefore == 0 {
		c.ACME.RenewBefore = 30 * 24 * time.Hour
	}
	if c.Storage.CertsDir == "" {
		c.Storage.CertsDir = "certs"
	}
	if c.Storage.AccountFile == "" {
		c.Storage.AccountFile = "account.json"
	}
	if c.Storage.AccountKeyFile == "" {
		c.Storage.AccountKeyFile = "account.key"
	}
	if c.Storage.CertFileMode == 0 {
		c.Storage.CertFileMode = 0600
	}
	if c.Storage.KeyFileMode == 0 {
		c.Storage.KeyFileMode = 0600
	}
	if c.Storage.DirMode == 0 {
		c.Storage.DirMode = 0700
	}

	return nil
}

// GetDefaultConfig returns a configuration with default values
func GetDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			ListenAddr:        "127.0.0.1:6060",
			ReadHeaderTimeout: 10 * time.Second,
			EndpointPath:      "/getcert",
		},
		ACME: ACMEConfig{
			AccountEmail: "",
			DataDir:      "./data",
			DirectoryURL: "",
			RenewBefore:  30 * 24 * time.Hour,
			KeyType:      "EC256",
		},
		DNS: DNSConfig{
			Provider: "tencentcloud",
			TencentCloud: TencentCloudConfig{
				SecretID:  "",
				SecretKey: "",
			},
			RecursiveNameservers: []string{
				"**************",
				"*********",
				"************",
			},
		},
		Security: SecurityConfig{
			DomainWhitelist:  []string{},
			AllowedIPs:       []string{},
			ValidateRequests: false,
		},
		Logging: LoggingConfig{
			Level:       "info",
			Format:      "text",
			LogRequests: true,
		},
		Storage: StorageConfig{
			CertsDir:       "certs",
			AccountFile:    "account.json",
			AccountKeyFile: "account.key",
			CertFileMode:   0600,
			KeyFileMode:    0600,
			DirMode:        0700,
		},
	}
}
